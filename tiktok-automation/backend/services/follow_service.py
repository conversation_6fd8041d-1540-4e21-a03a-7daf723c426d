"""
Follow Service - <PERSON>les TikTok follow automation
"""

import asyncio
import random
from typing import Dict, Any, Optional
from loguru import logger
from sqlalchemy.orm import Session

from models.browser_profile import BrowserProfile
from models.follow_settings import FollowSettings
from camoufox_integration.browser_manager import Browser<PERSON>anager
from core.database import get_db


class FollowService:
    def __init__(self):
        self.active_tasks: Dict[int, Dict[str, Any]] = {}
        self.browser_manager = BrowserManager()

    async def start_follow_task(self, profile_id: int, db: Session) -> Dict[str, Any]:
        """Start follow task for a profile"""
        try:
            # Get profile
            profile = db.query(BrowserProfile).filter(BrowserProfile.id == profile_id).first()
            if not profile:
                raise ValueError(f"Profile {profile_id} not found")

            if not profile.is_logged_in:
                raise ValueError(f"Profile {profile_id} is not logged in")

            # Get follow settings
            settings = db.query(FollowSettings).filter(
                FollowSettings.is_active == True
            ).order_by(FollowSettings.updated_at.desc()).first()

            if not settings:
                # Create default settings if none exist
                settings = FollowSettings(
                    target_profile_url="https://www.tiktok.com/@shoptaikhoangiarc",
                    videos_to_watch=3,
                    watch_time_seconds=30,
                    follows_per_day=50,
                    follows_per_session=10,
                    break_time_minutes=3600,
                    delay_between_follows_min=30,
                    delay_between_follows_max=60,
                    is_active=True
                )
                db.add(settings)
                db.commit()
                db.refresh(settings)
                logger.info("Created default follow settings")

            if not settings.target_profile_url:
                raise ValueError("Target profile URL not configured")

            # Update profile status
            profile.status = "running"
            profile.current_action = "Đang thực hiện follow..."
            db.commit()

            # Create browser instance and context (visible for follow automation)
            browser = await self.browser_manager.create_browser_instance(
                profile,
                headless=False  # Show browser for follow automation
            )
            context = await self.browser_manager.create_browser_context(browser, profile)
            page = await context.new_page()

            # Store task info
            self.active_tasks[profile_id] = {
                "status": "running",
                "browser": browser,
                "context": context,
                "page": page,
                "settings": settings.to_dict(),
                "follows_completed": 0,
                "videos_watched": 0,
                "start_time": asyncio.get_event_loop().time()
            }

            # Start follow task in background
            asyncio.create_task(self._execute_follow_task(profile_id, db))

            logger.info(f"Follow task started for profile {profile_id}")
            return {
                "success": True,
                "message": f"Follow task started for profile {profile.name}",
                "profile_id": profile_id,
                "target_url": settings.target_profile_url
            }

        except Exception as e:
            logger.error(f"Failed to start follow task for profile {profile_id}: {e}")
            # Update profile status back to ready on error
            if profile:
                profile.status = "ready"
                profile.current_action = "Sẵn sàng hoạt động"
                db.commit()
            raise

    async def pause_follow_task(self, profile_id: int, db: Session) -> Dict[str, Any]:
        """Pause follow task for a profile"""
        try:
            if profile_id not in self.active_tasks:
                raise ValueError(f"No active follow task for profile {profile_id}")

            # Update task status
            self.active_tasks[profile_id]["status"] = "paused"

            # Update profile status
            profile = db.query(BrowserProfile).filter(BrowserProfile.id == profile_id).first()
            if profile:
                profile.status = "paused"
                profile.current_action = "Đã tạm dừng"
                db.commit()

            logger.info(f"Follow task paused for profile {profile_id}")
            return {
                "success": True,
                "message": f"Follow task paused for profile {profile.name if profile else profile_id}",
                "profile_id": profile_id
            }

        except Exception as e:
            logger.error(f"Failed to pause follow task for profile {profile_id}: {e}")
            raise

    async def stop_follow_task(self, profile_id: int, db: Session) -> Dict[str, Any]:
        """Stop follow task for a profile"""
        try:
            if profile_id in self.active_tasks:
                task_info = self.active_tasks[profile_id]
                
                # Close browser if exists
                if "browser" in task_info:
                    try:
                        if "page" in task_info:
                            await task_info["page"].close()
                        if "context" in task_info:
                            await task_info["context"].close()
                        if "browser" in task_info:
                            await task_info["browser"].close()
                    except Exception as e:
                        logger.warning(f"Failed to close browser for profile {profile_id}: {e}")

                # Remove from active tasks
                del self.active_tasks[profile_id]

            # Update profile status
            profile = db.query(BrowserProfile).filter(BrowserProfile.id == profile_id).first()
            if profile:
                profile.status = "ready"
                profile.current_action = "Sẵn sàng hoạt động"
                db.commit()

            logger.info(f"Follow task stopped for profile {profile_id}")
            return {
                "success": True,
                "message": f"Follow task stopped for profile {profile.name if profile else profile_id}",
                "profile_id": profile_id
            }

        except Exception as e:
            logger.error(f"Failed to stop follow task for profile {profile_id}: {e}")
            raise

    async def _execute_follow_task(self, profile_id: int, db: Session):
        """Execute the follow task logic with human-like behavior"""
        try:
            task_info = self.active_tasks.get(profile_id)
            if not task_info:
                return

            page = task_info["page"]
            settings = task_info["settings"]

            # Navigate to target profile
            target_url = settings["target_profile_url"]
            logger.info(f"Navigating to target profile: {target_url}")

            if not page:
                logger.error(f"No page found for profile {profile_id}")
                return

            # Navigate to target profile with human-like delay
            await page.goto(target_url, wait_until="networkidle")
            await self._human_delay(3, 5)  # Wait 3-5 seconds like human

            # Scroll down to load content
            await self._human_scroll(page, 2)  # Scroll 2 times
            await self._human_delay(2, 3)

            # Find and click followers tab to get followers list
            followers_found = await self._click_followers_tab(page)
            if not followers_found:
                logger.warning("Could not find followers tab, trying alternative approach")
                # Try to find followers through profile page
                await self._find_followers_alternative(page)

            await self._human_delay(3, 5)

            # Start following users with video watching first
            follows_completed = 0
            max_follows = min(settings["follows_per_session"], settings["follows_per_day"])

            # Get list of users to follow
            user_elements = await self._get_user_elements(page)
            logger.info(f"Found {len(user_elements)} users to potentially follow")

            for i in range(min(max_follows, len(user_elements))):
                if task_info["status"] != "running":
                    break

                try:
                    # Step 1: Watch user's videos first (human-like behavior)
                    if settings["videos_to_watch"] > 0:
                        video_watched = await self._watch_user_videos_before_follow(page, user_elements[i], settings)
                        if not video_watched:
                            logger.warning(f"Could not watch videos for user {i+1}, skipping")
                            continue

                    # Step 2: Go back to followers list
                    await self._return_to_followers_list(page, target_url)
                    await self._human_delay(2, 3)

                    # Step 3: Follow the user
                    success = await self._follow_user_by_index(page, i)
                    if success:
                        follows_completed += 1
                        task_info["follows_completed"] = follows_completed
                        logger.info(f"Successfully followed user {i+1}/{max_follows}")

                        # Human-like delay between follows
                        await self._human_delay(
                            settings.get("delay_between_follows_min", 30),
                            settings.get("delay_between_follows_max", 60)
                        )
                    else:
                        logger.warning(f"Failed to follow user {i+1}")
                        await self._human_delay(5, 10)  # Wait before retry

                except Exception as e:
                    logger.error(f"Error processing user {i+1}: {e}")
                    await self._human_delay(3, 5)
                    continue

            logger.info(f"Follow task completed for profile {profile_id}. Followed {follows_completed} users")

            # Auto-stop after completing session
            await self.stop_follow_task(profile_id, db)

        except Exception as e:
            logger.error(f"Error in follow task execution for profile {profile_id}: {e}")
            # Stop task on error
            await self.stop_follow_task(profile_id, db)

    async def _human_delay(self, min_seconds: float, max_seconds: float):
        """Add human-like random delay"""
        delay = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(delay)

    async def _human_scroll(self, page, times: int = 3):
        """Scroll page like human"""
        for _ in range(times):
            # Random scroll amount
            scroll_amount = random.randint(300, 800)
            await page.evaluate(f"window.scrollBy(0, {scroll_amount})")
            await self._human_delay(1, 2)

    async def _click_followers_tab(self, page):
        """Click on followers tab"""
        try:
            # Try different selectors for followers tab
            selectors = [
                'a[href*="followers"]',
                'div[data-e2e="followers-tab"]',
                'span:has-text("Followers")',
                'a:has-text("Followers")',
                '[data-e2e="followers-count"]',
                'div:has-text("Followers")',
                'span:has-text("người theo dõi")',  # Vietnamese
                'a:has-text("người theo dõi")'
            ]

            for selector in selectors:
                try:
                    element = await page.wait_for_selector(selector, timeout=3000)
                    if element:
                        await element.click()
                        logger.info(f"Successfully clicked followers tab with selector: {selector}")
                        return True
                except:
                    continue

            logger.warning("Could not find followers tab")
            return False

        except Exception as e:
            logger.error(f"Error clicking followers tab: {e}")
            return False

    async def _find_followers_alternative(self, page):
        """Alternative method to find followers"""
        try:
            # Try to scroll and look for follower count
            await self._human_scroll(page, 3)
            await self._human_delay(2, 3)

            # Look for follower count elements
            follower_selectors = [
                '[data-e2e="followers-count"]',
                'strong:has-text("Followers")',
                'div:has-text("Followers")',
                'span:has-text("người theo dõi")'
            ]

            for selector in follower_selectors:
                try:
                    element = await page.wait_for_selector(selector, timeout=2000)
                    if element:
                        await element.click()
                        logger.info("Found followers through alternative method")
                        return True
                except:
                    continue

            return False

        except Exception as e:
            logger.error(f"Error in alternative followers search: {e}")
            return False

    async def _get_user_elements(self, page):
        """Get list of user elements from followers page"""
        try:
            # Wait for followers list to load
            await page.wait_for_selector('[data-e2e="followers-container"], .user-item, [data-e2e="user-item"]', timeout=10000)
            await self._human_delay(2, 3)

            # Try different selectors for user elements
            user_selectors = [
                '[data-e2e="user-item"]',
                '.user-item',
                '[data-e2e="followers-container"] > div',
                'div[data-e2e*="user"]',
                'div:has([data-e2e="follow-button"])'
            ]

            for selector in user_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements and len(elements) > 0:
                        logger.info(f"Found {len(elements)} user elements with selector: {selector}")
                        return elements[:20]  # Limit to first 20 users
                except:
                    continue

            # Fallback: look for any elements with follow buttons
            follow_buttons = await page.query_selector_all('button:has-text("Follow"), button:has-text("Theo dõi")')
            if follow_buttons:
                logger.info(f"Found {len(follow_buttons)} follow buttons as fallback")
                return follow_buttons[:20]

            logger.warning("Could not find any user elements")
            return []

        except Exception as e:
            logger.error(f"Error getting user elements: {e}")
            return []

    async def _watch_user_videos_before_follow(self, page, user_element, settings: dict) -> bool:
        """Watch user's videos before following (human-like behavior)"""
        try:
            videos_to_watch = settings["videos_to_watch"]
            watch_time = settings["watch_time_seconds"]

            # Find user profile link or username
            user_link = None
            try:
                # Try to find profile link in user element
                link_element = await user_element.query_selector('a[href*="/@"]')
                if link_element:
                    user_link = await link_element.get_attribute('href')
                else:
                    # Try to find username and construct link
                    username_element = await user_element.query_selector('[data-e2e="user-title"], .username, a[href*="/@"]')
                    if username_element:
                        username_text = await username_element.text_content()
                        if username_text and username_text.startswith('@'):
                            user_link = f"https://www.tiktok.com/{username_text}"
                        elif username_text:
                            user_link = f"https://www.tiktok.com/@{username_text}"
            except:
                pass

            if not user_link:
                logger.warning("Could not find user profile link")
                return False

            # Navigate to user profile
            logger.info(f"Navigating to user profile: {user_link}")
            await page.goto(user_link, wait_until="networkidle")
            await self._human_delay(2, 4)

            # Scroll to load videos
            await self._human_scroll(page, 2)
            await self._human_delay(1, 2)

            # Find video links
            video_selectors = [
                'a[href*="/video/"]',
                'div[data-e2e="user-post-item"] a',
                '.video-feed-item a',
                'div:has([data-e2e="video-play-button"]) a'
            ]

            video_links = []
            for selector in video_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        video_links = elements[:videos_to_watch]
                        break
                except:
                    continue

            if not video_links:
                logger.warning("No videos found on user profile")
                return False

            # Watch videos
            videos_watched = 0
            for i, video_link in enumerate(video_links[:videos_to_watch]):
                try:
                    # Click on video
                    await video_link.click()
                    await self._human_delay(2, 3)

                    # Watch video for specified time with random variation
                    watch_duration = random.uniform(watch_time * 0.8, watch_time * 1.2)
                    logger.info(f"Watching video {i+1}/{videos_to_watch} for {watch_duration:.1f} seconds")

                    # Simulate human watching behavior
                    await self._simulate_video_watching(page, watch_duration)

                    # Random interactions
                    if random.random() < 0.3:  # 30% chance to like
                        await self._try_like_video(page)

                    if random.random() < 0.1:  # 10% chance to comment
                        await self._try_comment_video(page)

                    videos_watched += 1

                    # Go back to profile if not last video
                    if i < len(video_links) - 1:
                        await page.go_back()
                        await self._human_delay(1, 2)

                except Exception as e:
                    logger.warning(f"Error watching video {i+1}: {e}")
                    continue

            logger.info(f"Watched {videos_watched} videos for user")
            return videos_watched > 0

        except Exception as e:
            logger.error(f"Error in watch videos before follow: {e}")
            return False

    async def _simulate_video_watching(self, page, duration: float):
        """Simulate human video watching behavior"""
        try:
            # Split watch time into smaller chunks with interactions
            chunks = max(1, int(duration / 10))  # 10-second chunks
            chunk_duration = duration / chunks

            for i in range(chunks):
                await asyncio.sleep(chunk_duration)

                # Random scroll or interaction
                if random.random() < 0.3:
                    # Small scroll
                    scroll_amount = random.randint(50, 200)
                    await page.evaluate(f"window.scrollBy(0, {scroll_amount})")
                    await self._human_delay(0.5, 1)

                # Random pause/play (very low chance)
                if random.random() < 0.05:
                    try:
                        video_element = await page.query_selector('video')
                        if video_element:
                            await video_element.click()  # Pause
                            await self._human_delay(1, 2)
                            await video_element.click()  # Play
                    except:
                        pass

        except Exception as e:
            logger.warning(f"Error in video watching simulation: {e}")

    async def _return_to_followers_list(self, page, target_url: str):
        """Return to followers list after watching videos"""
        try:
            # Go back to target profile
            await page.goto(target_url, wait_until="networkidle")
            await self._human_delay(2, 3)

            # Click followers tab again
            await self._click_followers_tab(page)
            await self._human_delay(2, 3)

        except Exception as e:
            logger.error(f"Error returning to followers list: {e}")

    async def _follow_user_by_index(self, page, index: int) -> bool:
        """Follow user by index in the followers list"""
        try:
            # Get updated user elements
            user_elements = await self._get_user_elements(page)

            if index >= len(user_elements):
                logger.warning(f"User index {index} out of range")
                return False

            user_element = user_elements[index]

            # Find follow button in this user element
            follow_button = None
            follow_selectors = [
                'button:has-text("Follow")',
                'button:has-text("Theo dõi")',
                '[data-e2e="follow-button"]',
                'button[data-e2e*="follow"]'
            ]

            for selector in follow_selectors:
                try:
                    follow_button = await user_element.query_selector(selector)
                    if follow_button:
                        break
                except:
                    continue

            if not follow_button:
                logger.warning(f"No follow button found for user {index}")
                return False

            # Scroll to button and click
            await follow_button.scroll_into_view_if_needed()
            await self._human_delay(0.5, 1.5)

            await follow_button.click()
            await self._human_delay(1, 2)

            logger.info(f"Successfully followed user at index {index}")
            return True

        except Exception as e:
            logger.error(f"Error following user by index {index}: {e}")
            return False

    async def _follow_next_user(self, page, index: int) -> bool:
        """Follow the next user in the list"""
        try:
            # Wait for followers list to load
            await page.wait_for_selector('[data-e2e="followers-container"]', timeout=10000)

            # Find follow buttons
            follow_buttons = await page.query_selector_all('button:has-text("Follow")')

            if index < len(follow_buttons):
                button = follow_buttons[index]

                # Scroll to button
                await button.scroll_into_view_if_needed()
                await self._human_delay(0.5, 1.5)

                # Click follow button with human-like behavior
                await button.click()
                await self._human_delay(1, 2)

                logger.info(f"Successfully followed user {index + 1}")
                return True
            else:
                logger.warning(f"No more users to follow (index: {index})")
                return False

        except Exception as e:
            logger.error(f"Error following user {index + 1}: {e}")
            return False

    async def _watch_user_videos(self, page, settings: dict):
        """Watch user's videos to appear more human"""
        try:
            videos_to_watch = settings["videos_to_watch"]
            watch_time = settings["watch_time_seconds"]

            # Find video links
            video_links = await page.query_selector_all('a[href*="/video/"]')

            for i in range(min(videos_to_watch, len(video_links))):
                try:
                    # Click on video
                    await video_links[i].click()
                    await self._human_delay(2, 3)

                    # Watch video for specified time
                    watch_duration = random.uniform(watch_time * 0.8, watch_time * 1.2)
                    await asyncio.sleep(watch_duration)

                    # Random interactions
                    if random.random() < 0.3:  # 30% chance to like
                        await self._try_like_video(page)

                    if random.random() < 0.1:  # 10% chance to comment
                        await self._try_comment_video(page)

                    # Go back to profile
                    await page.go_back()
                    await self._human_delay(1, 2)

                except Exception as e:
                    logger.warning(f"Error watching video {i+1}: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error in watch videos: {e}")

    async def _try_like_video(self, page):
        """Try to like video"""
        try:
            like_button = await page.query_selector('[data-e2e="like-button"]')
            if like_button:
                await like_button.click()
                await self._human_delay(0.5, 1)
        except:
            pass

    async def _try_comment_video(self, page):
        """Try to comment on video"""
        try:
            # Simple positive comments
            comments = [
                "Nice! 👍",
                "Great content!",
                "Love this! ❤️",
                "Amazing! 🔥",
                "So good! 😍"
            ]

            comment_input = await page.query_selector('[data-e2e="comment-input"]')
            if comment_input:
                comment = random.choice(comments)
                await comment_input.fill(comment)
                await self._human_delay(1, 2)

                # Find and click post button
                post_button = await page.query_selector('[data-e2e="post-comment"]')
                if post_button:
                    await post_button.click()
                    await self._human_delay(1, 2)
        except:
            pass

    def get_task_status(self, profile_id: int) -> Optional[Dict[str, Any]]:
        """Get status of follow task for a profile"""
        return self.active_tasks.get(profile_id)


# Global instance
follow_service = FollowService()
