"""
Follow Service - <PERSON>les TikTok follow automation
"""

import asyncio
import random
from typing import Dict, Any, Optional
from loguru import logger
from sqlalchemy.orm import Session

from models.browser_profile import BrowserProfile
from models.follow_settings import FollowSettings
from camoufox_integration.browser_manager import Browser<PERSON>anager
from core.database import get_db


class FollowService:
    def __init__(self):
        self.active_tasks: Dict[int, Dict[str, Any]] = {}
        self.browser_manager = BrowserManager()

    async def start_follow_task(self, profile_id: int, db: Session) -> Dict[str, Any]:
        """Start follow task for a profile"""
        try:
            # Get profile
            profile = db.query(BrowserProfile).filter(BrowserProfile.id == profile_id).first()
            if not profile:
                raise ValueError(f"Profile {profile_id} not found")

            if not profile.is_logged_in:
                raise ValueError(f"Profile {profile_id} is not logged in")

            # Get follow settings
            settings = db.query(FollowSettings).filter(
                FollowSettings.is_active == True
            ).order_by(FollowSettings.updated_at.desc()).first()

            if not settings:
                # Create default settings if none exist
                settings = FollowSettings(
                    target_profile_url="https://www.tiktok.com/@shoptaikhoangiarc",
                    videos_to_watch=3,
                    watch_time_seconds=30,
                    follows_per_day=50,
                    follows_per_session=10,
                    break_time_minutes=3600,
                    delay_between_follows_min=30,
                    delay_between_follows_max=60,
                    is_active=True
                )
                db.add(settings)
                db.commit()
                db.refresh(settings)
                logger.info("Created default follow settings")

            if not settings.target_profile_url:
                raise ValueError("Target profile URL not configured")

            # Update profile status
            profile.status = "running"
            profile.current_action = "Đang thực hiện follow..."
            db.commit()

            # Create browser instance and context
            browser = await self.browser_manager.create_browser_instance(profile)
            context = await self.browser_manager.create_browser_context(browser, profile)
            page = await context.new_page()

            # Store task info
            self.active_tasks[profile_id] = {
                "status": "running",
                "browser": browser,
                "context": context,
                "page": page,
                "settings": settings.to_dict(),
                "follows_completed": 0,
                "videos_watched": 0,
                "start_time": asyncio.get_event_loop().time()
            }

            # Start follow task in background
            asyncio.create_task(self._execute_follow_task(profile_id, db))

            logger.info(f"Follow task started for profile {profile_id}")
            return {
                "success": True,
                "message": f"Follow task started for profile {profile.name}",
                "profile_id": profile_id,
                "target_url": settings.target_profile_url
            }

        except Exception as e:
            logger.error(f"Failed to start follow task for profile {profile_id}: {e}")
            # Update profile status back to ready on error
            if profile:
                profile.status = "ready"
                profile.current_action = "Sẵn sàng hoạt động"
                db.commit()
            raise

    async def pause_follow_task(self, profile_id: int, db: Session) -> Dict[str, Any]:
        """Pause follow task for a profile"""
        try:
            if profile_id not in self.active_tasks:
                raise ValueError(f"No active follow task for profile {profile_id}")

            # Update task status
            self.active_tasks[profile_id]["status"] = "paused"

            # Update profile status
            profile = db.query(BrowserProfile).filter(BrowserProfile.id == profile_id).first()
            if profile:
                profile.status = "paused"
                profile.current_action = "Đã tạm dừng"
                db.commit()

            logger.info(f"Follow task paused for profile {profile_id}")
            return {
                "success": True,
                "message": f"Follow task paused for profile {profile.name if profile else profile_id}",
                "profile_id": profile_id
            }

        except Exception as e:
            logger.error(f"Failed to pause follow task for profile {profile_id}: {e}")
            raise

    async def stop_follow_task(self, profile_id: int, db: Session) -> Dict[str, Any]:
        """Stop follow task for a profile"""
        try:
            if profile_id in self.active_tasks:
                task_info = self.active_tasks[profile_id]
                
                # Close browser if exists
                if "browser" in task_info:
                    try:
                        if "page" in task_info:
                            await task_info["page"].close()
                        if "context" in task_info:
                            await task_info["context"].close()
                        if "browser" in task_info:
                            await task_info["browser"].close()
                    except Exception as e:
                        logger.warning(f"Failed to close browser for profile {profile_id}: {e}")

                # Remove from active tasks
                del self.active_tasks[profile_id]

            # Update profile status
            profile = db.query(BrowserProfile).filter(BrowserProfile.id == profile_id).first()
            if profile:
                profile.status = "ready"
                profile.current_action = "Sẵn sàng hoạt động"
                db.commit()

            logger.info(f"Follow task stopped for profile {profile_id}")
            return {
                "success": True,
                "message": f"Follow task stopped for profile {profile.name if profile else profile_id}",
                "profile_id": profile_id
            }

        except Exception as e:
            logger.error(f"Failed to stop follow task for profile {profile_id}: {e}")
            raise

    async def _execute_follow_task(self, profile_id: int, db: Session):
        """Execute the follow task logic with human-like behavior"""
        try:
            task_info = self.active_tasks.get(profile_id)
            if not task_info:
                return

            page = task_info["page"]
            settings = task_info["settings"]

            # Navigate to target profile
            target_url = settings["target_profile_url"]
            logger.info(f"Navigating to target profile: {target_url}")

            if not page:
                logger.error(f"No page found for profile {profile_id}")
                return

            # Navigate to target profile with human-like delay
            await page.goto(target_url, wait_until="networkidle")
            await self._human_delay(2, 4)  # Wait 2-4 seconds like human

            # Scroll down to load followers
            await self._human_scroll(page, 3)  # Scroll 3 times

            # Find and click followers tab
            await self._click_followers_tab(page)
            await self._human_delay(2, 3)

            # Start following users
            follows_completed = 0
            max_follows = min(settings["follows_per_session"], settings["follows_per_day"])

            for i in range(max_follows):
                if task_info["status"] != "running":
                    break

                success = await self._follow_next_user(page, i)
                if success:
                    follows_completed += 1
                    task_info["follows_completed"] = follows_completed

                    # Watch videos if configured
                    if settings["videos_to_watch"] > 0:
                        await self._watch_user_videos(page, settings)

                    # Human-like delay between follows
                    await self._human_delay(
                        settings.get("delay_between_follows_min", 30),
                        settings.get("delay_between_follows_max", 60)
                    )
                else:
                    logger.warning(f"Failed to follow user {i+1}")
                    await self._human_delay(5, 10)  # Wait before retry

            logger.info(f"Follow task completed for profile {profile_id}. Followed {follows_completed} users")

            # Auto-stop after completing session
            await self.stop_follow_task(profile_id, db)

        except Exception as e:
            logger.error(f"Error in follow task execution for profile {profile_id}: {e}")
            # Stop task on error
            await self.stop_follow_task(profile_id, db)

    async def _human_delay(self, min_seconds: float, max_seconds: float):
        """Add human-like random delay"""
        delay = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(delay)

    async def _human_scroll(self, page, times: int = 3):
        """Scroll page like human"""
        for _ in range(times):
            # Random scroll amount
            scroll_amount = random.randint(300, 800)
            await page.evaluate(f"window.scrollBy(0, {scroll_amount})")
            await self._human_delay(1, 2)

    async def _click_followers_tab(self, page):
        """Click on followers tab"""
        try:
            # Try different selectors for followers tab
            selectors = [
                'a[href*="followers"]',
                'div[data-e2e="followers-tab"]',
                'span:has-text("Followers")',
                'a:has-text("Followers")'
            ]

            for selector in selectors:
                try:
                    await page.click(selector, timeout=5000)
                    logger.info("Successfully clicked followers tab")
                    return True
                except:
                    continue

            logger.warning("Could not find followers tab")
            return False

        except Exception as e:
            logger.error(f"Error clicking followers tab: {e}")
            return False

    async def _follow_next_user(self, page, index: int) -> bool:
        """Follow the next user in the list"""
        try:
            # Wait for followers list to load
            await page.wait_for_selector('[data-e2e="followers-container"]', timeout=10000)

            # Find follow buttons
            follow_buttons = await page.query_selector_all('button:has-text("Follow")')

            if index < len(follow_buttons):
                button = follow_buttons[index]

                # Scroll to button
                await button.scroll_into_view_if_needed()
                await self._human_delay(0.5, 1.5)

                # Click follow button with human-like behavior
                await button.click()
                await self._human_delay(1, 2)

                logger.info(f"Successfully followed user {index + 1}")
                return True
            else:
                logger.warning(f"No more users to follow (index: {index})")
                return False

        except Exception as e:
            logger.error(f"Error following user {index + 1}: {e}")
            return False

    async def _watch_user_videos(self, page, settings: dict):
        """Watch user's videos to appear more human"""
        try:
            videos_to_watch = settings["videos_to_watch"]
            watch_time = settings["watch_time_seconds"]

            # Find video links
            video_links = await page.query_selector_all('a[href*="/video/"]')

            for i in range(min(videos_to_watch, len(video_links))):
                try:
                    # Click on video
                    await video_links[i].click()
                    await self._human_delay(2, 3)

                    # Watch video for specified time
                    watch_duration = random.uniform(watch_time * 0.8, watch_time * 1.2)
                    await asyncio.sleep(watch_duration)

                    # Random interactions
                    if random.random() < 0.3:  # 30% chance to like
                        await self._try_like_video(page)

                    if random.random() < 0.1:  # 10% chance to comment
                        await self._try_comment_video(page)

                    # Go back to profile
                    await page.go_back()
                    await self._human_delay(1, 2)

                except Exception as e:
                    logger.warning(f"Error watching video {i+1}: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error in watch videos: {e}")

    async def _try_like_video(self, page):
        """Try to like video"""
        try:
            like_button = await page.query_selector('[data-e2e="like-button"]')
            if like_button:
                await like_button.click()
                await self._human_delay(0.5, 1)
        except:
            pass

    async def _try_comment_video(self, page):
        """Try to comment on video"""
        try:
            # Simple positive comments
            comments = [
                "Nice! 👍",
                "Great content!",
                "Love this! ❤️",
                "Amazing! 🔥",
                "So good! 😍"
            ]

            comment_input = await page.query_selector('[data-e2e="comment-input"]')
            if comment_input:
                comment = random.choice(comments)
                await comment_input.fill(comment)
                await self._human_delay(1, 2)

                # Find and click post button
                post_button = await page.query_selector('[data-e2e="post-comment"]')
                if post_button:
                    await post_button.click()
                    await self._human_delay(1, 2)
        except:
            pass

    def get_task_status(self, profile_id: int) -> Optional[Dict[str, Any]]:
        """Get status of follow task for a profile"""
        return self.active_tasks.get(profile_id)


# Global instance
follow_service = FollowService()
