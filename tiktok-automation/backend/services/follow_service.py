"""
Follow Service - <PERSON><PERSON> TikTok follow automation
"""

import asyncio
import random
from typing import Dict, Any, Optional
from loguru import logger
from sqlalchemy.orm import Session

from models.browser_profile import BrowserProfile
from models.follow_settings import FollowSettings
from camoufox_integration.browser_manager import Browser<PERSON>anager
from core.database import get_db


class FollowService:
    def __init__(self):
        self.active_tasks: Dict[int, Dict[str, Any]] = {}
        self.browser_manager = BrowserManager()

    async def start_follow_task(self, profile_id: int, db: Session) -> Dict[str, Any]:
        """Start follow task for a profile"""
        try:
            # Get profile
            profile = db.query(BrowserProfile).filter(BrowserProfile.id == profile_id).first()
            if not profile:
                raise ValueError(f"Profile {profile_id} not found")

            if not profile.is_logged_in:
                raise ValueError(f"Profile {profile_id} is not logged in")

            # Get follow settings
            settings = db.query(FollowSettings).filter(
                FollowSettings.is_active == True
            ).order_by(FollowSettings.updated_at.desc()).first()

            if not settings or not settings.target_profile_url:
                raise ValueError("Follow settings not configured")

            # Update profile status
            profile.status = "running"
            profile.current_action = "Đang thực hiện follow..."
            db.commit()

            # Create browser instance
            browser_instance = await self.browser_manager.create_browser_instance(profile)
            
            # Store task info
            self.active_tasks[profile_id] = {
                "status": "running",
                "browser_instance": browser_instance,
                "settings": settings.to_dict(),
                "follows_completed": 0,
                "videos_watched": 0,
                "start_time": asyncio.get_event_loop().time()
            }

            # Start follow task in background
            asyncio.create_task(self._execute_follow_task(profile_id, db))

            logger.info(f"Follow task started for profile {profile_id}")
            return {
                "success": True,
                "message": f"Follow task started for profile {profile.name}",
                "profile_id": profile_id,
                "target_url": settings.target_profile_url
            }

        except Exception as e:
            logger.error(f"Failed to start follow task for profile {profile_id}: {e}")
            # Update profile status back to ready on error
            if profile:
                profile.status = "ready"
                profile.current_action = "Sẵn sàng hoạt động"
                db.commit()
            raise

    async def pause_follow_task(self, profile_id: int, db: Session) -> Dict[str, Any]:
        """Pause follow task for a profile"""
        try:
            if profile_id not in self.active_tasks:
                raise ValueError(f"No active follow task for profile {profile_id}")

            # Update task status
            self.active_tasks[profile_id]["status"] = "paused"

            # Update profile status
            profile = db.query(BrowserProfile).filter(BrowserProfile.id == profile_id).first()
            if profile:
                profile.status = "paused"
                profile.current_action = "Đã tạm dừng"
                db.commit()

            logger.info(f"Follow task paused for profile {profile_id}")
            return {
                "success": True,
                "message": f"Follow task paused for profile {profile.name if profile else profile_id}",
                "profile_id": profile_id
            }

        except Exception as e:
            logger.error(f"Failed to pause follow task for profile {profile_id}: {e}")
            raise

    async def stop_follow_task(self, profile_id: int, db: Session) -> Dict[str, Any]:
        """Stop follow task for a profile"""
        try:
            if profile_id in self.active_tasks:
                task_info = self.active_tasks[profile_id]
                
                # Close browser if exists
                if "browser_instance" in task_info:
                    try:
                        await self.browser_manager.close_browser_instance(task_info["browser_instance"])
                    except Exception as e:
                        logger.warning(f"Failed to close browser for profile {profile_id}: {e}")

                # Remove from active tasks
                del self.active_tasks[profile_id]

            # Update profile status
            profile = db.query(BrowserProfile).filter(BrowserProfile.id == profile_id).first()
            if profile:
                profile.status = "ready"
                profile.current_action = "Sẵn sàng hoạt động"
                db.commit()

            logger.info(f"Follow task stopped for profile {profile_id}")
            return {
                "success": True,
                "message": f"Follow task stopped for profile {profile.name if profile else profile_id}",
                "profile_id": profile_id
            }

        except Exception as e:
            logger.error(f"Failed to stop follow task for profile {profile_id}: {e}")
            raise

    async def _execute_follow_task(self, profile_id: int, db: Session):
        """Execute the follow task logic"""
        try:
            task_info = self.active_tasks.get(profile_id)
            if not task_info:
                return

            browser_instance = task_info["browser_instance"]
            settings = task_info["settings"]
            
            # Navigate to target profile
            target_url = settings["target_profile_url"]
            logger.info(f"Navigating to target profile: {target_url}")
            
            # TODO: Implement actual follow logic here
            # This is a placeholder for the follow automation logic
            
            # Simulate follow process
            await asyncio.sleep(2)  # Wait for page load
            
            # Update task completion
            task_info["follows_completed"] += 1
            
            logger.info(f"Follow task completed for profile {profile_id}")
            
            # Auto-stop after completing session
            await self.stop_follow_task(profile_id, db)

        except Exception as e:
            logger.error(f"Error in follow task execution for profile {profile_id}: {e}")
            # Stop task on error
            await self.stop_follow_task(profile_id, db)

    def get_task_status(self, profile_id: int) -> Optional[Dict[str, Any]]:
        """Get status of follow task for a profile"""
        return self.active_tasks.get(profile_id)


# Global instance
follow_service = FollowService()
