import { apiClient } from './apiClient';

class SettingsService {
  async getFollowSettings() {
    try {
      // For now, use localStorage until backend API is ready
      const saved = localStorage.getItem('followSettings');
      if (saved) {
        return JSON.parse(saved);
      }

      // Return default settings
      return {
        targetProfileUrl: 'https://www.tiktok.com/@shoptaikhoangiarc',
        videosToWatch: 3,
        watchTimeSeconds: 30,
        followsPerDay: 50,
        followsPerSession: 10,
        breakTimeMinutes: 3600,
      };
    } catch (error) {
      console.error('Failed to get follow settings:', error);
      // Return default settings if error
      return {
        targetProfileUrl: 'https://www.tiktok.com/@shoptaikhoangiarc',
        videosToWatch: 3,
        watchTimeSeconds: 30,
        followsPerDay: 50,
        followsPerSession: 10,
        breakTimeMinutes: 3600,
      };
    }
  }

  async saveFollowSettings(settings) {
    try {
      // For now, save to localStorage until backend API is ready
      localStorage.setItem('followSettings', JSON.stringify(settings));
      return { success: true, message: 'Settings saved successfully' };
    } catch (error) {
      console.error('Failed to save follow settings:', error);
      throw error;
    }
  }

  async getSystemSettings() {
    try {
      const response = await apiClient.get('/api/v1/settings/system');
      return response.data;
    } catch (error) {
      console.error('Failed to get system settings:', error);
      throw error;
    }
  }

  async saveSystemSettings(settings) {
    try {
      const response = await apiClient.post('/api/v1/settings/system', settings);
      return response.data;
    } catch (error) {
      console.error('Failed to save system settings:', error);
      throw error;
    }
  }
}

export const settingsService = new SettingsService();
