import { apiClient } from './apiClient';

class SettingsService {
  async getFollowSettings() {
    try {
      const response = await apiClient.get('/api/v1/settings/follow');
      return response.data;
    } catch (error) {
      console.error('Failed to get follow settings:', error);
      // Return default settings if API fails
      return {
        targetProfileUrl: '',
        videosToWatch: 3,
        watchTimeSeconds: 30,
        followsPerDay: 50,
        followsPerSession: 10,
        breakTimeMinutes: 3600,
      };
    }
  }

  async saveFollowSettings(settings) {
    try {
      const response = await apiClient.post('/api/v1/settings/follow', settings);
      return response.data;
    } catch (error) {
      console.error('Failed to save follow settings:', error);
      throw error;
    }
  }

  async getSystemSettings() {
    try {
      const response = await apiClient.get('/api/v1/settings/system');
      return response.data;
    } catch (error) {
      console.error('Failed to get system settings:', error);
      throw error;
    }
  }

  async saveSystemSettings(settings) {
    try {
      const response = await apiClient.post('/api/v1/settings/system', settings);
      return response.data;
    } catch (error) {
      console.error('Failed to save system settings:', error);
      throw error;
    }
  }
}

export const settingsService = new SettingsService();
