/**
 * Profile Service - Manages browser profiles and proxy integration
 */

import { profileAPI, proxyAPI, handleApiError } from './api';

class ProfileService {
  constructor() {
    this.profiles = [];
    this.proxies = [];
    this.listeners = [];
  }

  // Event listener management
  addListener(callback) {
    this.listeners.push(callback);
  }

  removeListener(callback) {
    this.listeners = this.listeners.filter(l => l !== callback);
  }

  notifyListeners(event, data) {
    this.listeners.forEach(listener => {
      try {
        listener(event, data);
      } catch (error) {
        console.error('Error in profile service listener:', error);
      }
    });
  }

  // Profile management
  async loadProfiles() {
    try {
      const profiles = await profileAPI.getProfiles();
      this.profiles = profiles.map(profile => this.transformProfileFromAPI(profile));

      // Always add mock data for testing UI
      const mockProfiles = this.getMockProfiles();
      this.profiles = [...this.profiles, ...mockProfiles];

      this.notifyListeners('profiles_loaded', this.profiles);
      return this.profiles;
    } catch (error) {
      console.error('Failed to load profiles from API, using mock data:', error);
      // Use mock data when API fails
      this.profiles = this.getMockProfiles();
      this.notifyListeners('profiles_loaded', this.profiles);
      return this.profiles;
    }
  }

  getMockProfiles() {
    const profiles = [
      {
        id: 1,
        stt: 1,
        username: 'profile_001',
        proxy: '192.168.1.100:8080',
        status: 'SẴN SÀNG',
        statusEn: 'ready',
        followersFollowed: 45,
        followersToday: 12,
        currentAction: 'Đang theo dõi @competitor1',
        isActive: true,
        isLoggedIn: true,
        is_logged_in: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 2,
        stt: 2,
        username: 'profile_002',
        proxy: '192.168.1.101:8080',
        status: 'ĐANG NHẬP HOẠT ĐỘ',
        statusEn: 'running',
        followersFollowed: 23,
        followersToday: 8,
        currentAction: 'Đang xem video @competitor2',
        isActive: true,
        isLoggedIn: true,
        is_logged_in: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 3,
        stt: 3,
        username: 'profile_003',
        proxy: 'Local Network',
        status: 'CHƯA ĐĂNG NHẬP',
        statusEn: 'inactive',
        followersFollowed: 0,
        followersToday: 0,
        currentAction: 'Chờ đăng nhập',
        isActive: false,
        isLoggedIn: false,
        is_logged_in: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 4,
        stt: 4,
        username: 'test_login_flow',
        proxy: 'Local Network',
        status: 'ĐANG ĐĂNG NHẬP',
        statusEn: 'logging_in',
        followersFollowed: 0,
        followersToday: 0,
        currentAction: 'Đang đăng nhập TikTok...',
        isActive: true,
        isLoggedIn: false,
        is_logged_in: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    // Calculate actions for each profile
    profiles.forEach(profile => {
      profile.actions = this.getAvailableActions(profile);
    });

    return profiles;
  }

  async createProfile(profileData) {
    try {
      // First create or find proxy if needed
      let proxyId = null;
      if (profileData.proxyType !== 'no-proxy') {
        proxyId = await this.getOrCreateProxy(profileData);
      }

      // Create profile
      const apiProfileData = {
        name: profileData.profileName,
        description: `Profile with ${profileData.proxyType} proxy`,
        proxy_id: proxyId,
        auto_generate_fingerprint: true,
        os_preference: 'windows',
        browser_preference: 'firefox'
      };

      const newProfile = await profileAPI.createProfile(apiProfileData);
      const transformedProfile = this.transformProfileFromAPI(newProfile);

      this.profiles.push(transformedProfile);
      this.notifyListeners('profile_created', transformedProfile);

      return transformedProfile;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Failed to create profile:', apiError);
      throw new Error(apiError.message);
    }
  }

  async getOrCreateProxy(profileData) {
    try {
      // Check if proxy already exists
      const existingProxies = await proxyAPI.getProxies();
      const existingProxy = existingProxies.find(p =>
        p.host === profileData.host &&
        p.port === parseInt(profileData.port) &&
        p.proxy_type === profileData.proxyType
      );

      if (existingProxy) {
        console.log('Using existing proxy:', existingProxy.id);
        return existingProxy.id;
      } else {
        // Create new proxy
        const proxy = await this.createProxyFromForm(profileData);
        console.log('Created new proxy:', proxy.id);
        return proxy.id;
      }
    } catch (error) {
      // If error is about duplicate proxy, try to find existing one
      if (error.message.includes('already exists')) {
        const existingProxies = await proxyAPI.getProxies();
        const existingProxy = existingProxies.find(p =>
          p.host === profileData.host &&
          p.port === parseInt(profileData.port) &&
          p.proxy_type === profileData.proxyType
        );

        if (existingProxy) {
          console.log('Found existing proxy after error:', existingProxy.id);
          return existingProxy.id;
        }
      }

      throw error;
    }
  }

  async updateProfile(profileId, updates) {
    try {
      const updatedProfile = await profileAPI.updateProfile(profileId, updates);
      const transformedProfile = this.transformProfileFromAPI(updatedProfile);
      
      const index = this.profiles.findIndex(p => p.id === profileId);
      if (index !== -1) {
        this.profiles[index] = transformedProfile;
        this.notifyListeners('profile_updated', transformedProfile);
      }
      
      return transformedProfile;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Failed to update profile:', apiError);
      throw new Error(apiError.message);
    }
  }

  async deleteProfile(profileId) {
    try {
      await profileAPI.deleteProfile(profileId);
      this.profiles = this.profiles.filter(p => p.id !== profileId);
      this.notifyListeners('profile_deleted', profileId);
      return true;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Failed to delete profile:', apiError);
      throw new Error(apiError.message);
    }
  }

  async testProfile(profileId) {
    try {
      const result = await profileAPI.testProfile(profileId);
      this.notifyListeners('profile_tested', { profileId, result });
      return result;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Failed to test profile:', apiError);
      throw new Error(apiError.message);
    }
  }

  // Proxy management
  async createProxyFromForm(formData) {
    try {
      const proxyData = {
        name: `${formData.profileName}_proxy`,
        proxy_type: formData.proxyType,
        host: formData.host,
        port: parseInt(formData.port),
        username: formData.username || null,
        password: formData.password || null,
        description: `Proxy for profile ${formData.profileName}`,
        validate_on_create: true
      };

      const proxy = await proxyAPI.createProxy(proxyData);
      return proxy;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Failed to create proxy:', apiError);
      throw new Error(apiError.message);
    }
  }

  async validateProxy(proxyData) {
    try {
      // Check if proxy already exists
      const existingProxies = await proxyAPI.getProxies();
      const existingProxy = existingProxies.find(p =>
        p.host === proxyData.host &&
        p.port === parseInt(proxyData.port) &&
        p.proxy_type === proxyData.proxyType
      );

      if (existingProxy) {
        // Use existing proxy for validation
        const result = await proxyAPI.validateProxy(existingProxy.id);
        return result;
      } else {
        // Create temporary proxy for validation with unique name
        const timestamp = Date.now();
        const tempProxy = await this.createProxyFromForm({
          ...proxyData,
          profileName: `temp_validation_${timestamp}`
        });

        // Validate the proxy
        const result = await proxyAPI.validateProxy(tempProxy.id);

        // Clean up temporary proxy
        await proxyAPI.deleteProxy(tempProxy.id);

        return result;
      }
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Failed to validate proxy:', apiError);

      // If error is about duplicate proxy, try to find and validate existing one
      if (apiError.message.includes('already exists')) {
        try {
          const existingProxies = await proxyAPI.getProxies();
          const existingProxy = existingProxies.find(p =>
            p.host === proxyData.host &&
            p.port === parseInt(proxyData.port) &&
            p.proxy_type === proxyData.proxyType
          );

          if (existingProxy) {
            const result = await proxyAPI.validateProxy(existingProxy.id);
            return result;
          }
        } catch (retryError) {
          console.error('Retry validation failed:', retryError);
        }
      }

      throw new Error(apiError.message);
    }
  }

  // Profile actions
  async startLogin(profileId) {
    try {
      const result = await profileAPI.loginProfile(profileId);

      if (result.success) {
        // Update local profile status
        const profileIndex = this.profiles.findIndex(p => p.id === profileId);
        if (profileIndex !== -1) {
          this.profiles[profileIndex].status = 'ĐANG ĐĂNG NHẬP';
          this.profiles[profileIndex].statusEn = 'logging_in';
          this.profiles[profileIndex].currentAction = 'Đang đăng nhập TikTok...';
          this.profiles[profileIndex].actions = this.getAvailableActions(this.profiles[profileIndex]);
        }

        this.notifyListeners('login_started', profileId);
      }

      return result;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Failed to start login:', apiError);
      throw new Error(apiError.message);
    }
  }

  async completeLogin(profileId) {
    try {
      const result = await profileAPI.completeLoginProfile(profileId);

      if (result.success) {
        // Update local profile status
        const profileIndex = this.profiles.findIndex(p => p.id === profileId);
        if (profileIndex !== -1) {
          this.profiles[profileIndex].status = 'SẴN SÀNG';
          this.profiles[profileIndex].statusEn = 'ready';
          this.profiles[profileIndex].currentAction = 'Sẵn sàng hoạt động';
          this.profiles[profileIndex].isLoggedIn = true;
          this.profiles[profileIndex].actions = this.getAvailableActions(this.profiles[profileIndex]);
        }

        this.notifyListeners('login_completed', profileId);
      }

      return result;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Failed to complete login:', apiError);
      throw new Error(apiError.message);
    }
  }

  async startFollowTask(profileId) {
    try {
      // Get follow settings from localStorage
      const settingsService = await import('./settingsService.js');
      const followSettings = await settingsService.default.getFollowSettings();

      console.log('Starting follow task with settings:', followSettings);

      const result = await profileAPI.startFollowTask(profileId, followSettings);

      if (result.success) {
        // Update local profile status
        const profileIndex = this.profiles.findIndex(p => p.id === profileId);
        if (profileIndex !== -1) {
          this.profiles[profileIndex].status = 'ĐANG CHẠY';
          this.profiles[profileIndex].statusEn = 'running';
          this.profiles[profileIndex].currentAction = 'Đang thực hiện follow...';
          this.profiles[profileIndex].actions = this.getAvailableActions(this.profiles[profileIndex]);
        }

        this.notifyListeners('follow_started', profileId);
      }

      return result;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Failed to start follow task:', apiError);
      throw new Error(apiError.message);
    }
  }

  async pauseFollowTask(profileId) {
    try {
      const result = await profileAPI.pauseFollowTask(profileId);

      if (result.success) {
        // Update local profile status
        const profileIndex = this.profiles.findIndex(p => p.id === profileId);
        if (profileIndex !== -1) {
          this.profiles[profileIndex].status = 'TẠM DỪNG';
          this.profiles[profileIndex].statusEn = 'paused';
          this.profiles[profileIndex].currentAction = 'Đã tạm dừng';
          this.profiles[profileIndex].actions = this.getAvailableActions(this.profiles[profileIndex]);
        }

        this.notifyListeners('follow_paused', profileId);
      }

      return result;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Failed to pause follow task:', apiError);
      throw new Error(apiError.message);
    }
  }

  async stopFollowTask(profileId) {
    try {
      const result = await profileAPI.stopFollowTask(profileId);

      if (result.success) {
        // Update local profile status
        const profileIndex = this.profiles.findIndex(p => p.id === profileId);
        if (profileIndex !== -1) {
          this.profiles[profileIndex].status = 'SẴN SÀNG';
          this.profiles[profileIndex].statusEn = 'ready';
          this.profiles[profileIndex].currentAction = 'Sẵn sàng hoạt động';
          this.profiles[profileIndex].actions = this.getAvailableActions(this.profiles[profileIndex]);
        }

        this.notifyListeners('follow_stopped', profileId);
      }

      return result;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Failed to stop follow task:', apiError);
      throw new Error(apiError.message);
    }
  }

  async startAutomation(profileId) {
    try {
      // Update profile status
      await this.updateProfile(profileId, { 
        status: 'running',
        current_action: 'Đang tương tác'
      });

      this.notifyListeners('automation_started', profileId);
      
      return { success: true, message: 'Automation started' };
    } catch (error) {
      console.error('Failed to start automation:', error);
      throw error;
    }
  }

  async pauseAutomation(profileId) {
    try {
      // Update profile status
      await this.updateProfile(profileId, { 
        status: 'paused',
        current_action: 'Đã tạm dừng'
      });

      this.notifyListeners('automation_paused', profileId);
      
      return { success: true, message: 'Automation paused' };
    } catch (error) {
      console.error('Failed to pause automation:', error);
      throw error;
    }
  }

  async stopAutomation(profileId) {
    try {
      // Update profile status
      await this.updateProfile(profileId, { 
        status: 'ready',
        current_action: 'Đã dừng'
      });

      this.notifyListeners('automation_stopped', profileId);
      
      return { success: true, message: 'Automation stopped' };
    } catch (error) {
      console.error('Failed to stop automation:', error);
      throw error;
    }
  }

  // Bulk operations
  async bulkAction(profileIds, action) {
    const results = [];
    
    for (const profileId of profileIds) {
      try {
        let result;
        switch (action) {
          case 'start':
            result = await this.startAutomation(profileId);
            break;
          case 'pause':
            result = await this.pauseAutomation(profileId);
            break;
          case 'stop':
            result = await this.stopAutomation(profileId);
            break;
          default:
            throw new Error(`Unknown action: ${action}`);
        }
        results.push({ profileId, success: true, result });
      } catch (error) {
        results.push({ profileId, success: false, error: error.message });
      }
    }

    this.notifyListeners('bulk_action_completed', { action, results });
    return results;
  }

  // Data transformation
  transformProfileFromAPI(apiProfile) {
    // Determine proxy display
    let proxyDisplay = 'Local Network';
    if (apiProfile.proxy_info) {
      proxyDisplay = `${apiProfile.proxy_info.host}:${apiProfile.proxy_info.port}`;
    } else if (apiProfile.proxy_id) {
      // If we have proxy_id but no proxy_info, we need to fetch it
      proxyDisplay = 'Loading proxy...';
      // Fetch proxy info asynchronously and update later
      this._fetchAndUpdateProxyInfo(apiProfile.id, apiProfile.proxy_id);
    }

    // Create profile object with English status for logic, Vietnamese for display
    const profile = {
      id: apiProfile.id,
      stt: apiProfile.id,
      username: apiProfile.name,
      proxy: proxyDisplay,
      proxy_id: apiProfile.proxy_id, // Store proxy_id for later use
      status: this.mapStatusToVietnamese(apiProfile.status || 'inactive'),
      statusEn: apiProfile.status || 'inactive', // Keep English status for logic
      followersFollowed: 0, // TODO: Get from automation data
      followersToday: 0, // TODO: Get from automation data
      currentAction: apiProfile.current_action || 'Chờ đăng nhập',
      isActive: apiProfile.is_active,
      isLoggedIn: apiProfile.is_logged_in || false,
      is_logged_in: apiProfile.is_logged_in || false, // Add snake_case version for compatibility
      createdAt: apiProfile.created_at,
      updatedAt: apiProfile.updated_at
    };

    // Calculate actions after profile object is created
    profile.actions = this.getAvailableActions(profile);

    return profile;
  }

  async _fetchAndUpdateProxyInfo(profileId, proxyId) {
    try {
      // Fetch proxy details
      const response = await fetch(`http://127.0.0.1:8000/api/v1/proxies/${proxyId}`);
      if (response.ok) {
        const proxyData = await response.json();

        // Update the profile in our local array
        const profileIndex = this.profiles.findIndex(p => p.id === profileId);
        if (profileIndex !== -1) {
          this.profiles[profileIndex].proxy = `${proxyData.host}:${proxyData.port}`;
          this.profiles[profileIndex].proxyInfo = proxyData; // Store full proxy info

          // Notify listeners about the update
          this.notifyListeners('profile_updated', this.profiles[profileIndex]);
        }
      } else {
        console.error('Failed to fetch proxy info for profile', profileId);
        // Update to show error
        const profileIndex = this.profiles.findIndex(p => p.id === profileId);
        if (profileIndex !== -1) {
          this.profiles[profileIndex].proxy = 'Proxy Error';
          this.notifyListeners('profile_updated', this.profiles[profileIndex]);
        }
      }
    } catch (error) {
      console.error('Error fetching proxy info:', error);
      // Update to show error
      const profileIndex = this.profiles.findIndex(p => p.id === profileId);
      if (profileIndex !== -1) {
        this.profiles[profileIndex].proxy = 'Proxy Error';
        this.notifyListeners('profile_updated', this.profiles[profileIndex]);
      }
    }
  }

  mapStatusToVietnamese(status) {
    const statusMap = {
      'inactive': 'CHƯA ĐĂNG NHẬP',
      'logging_in': 'ĐANG ĐĂNG NHẬP',
      'ready': 'SẴN SÀNG',
      'running': 'ĐANG NHẬP HOẠT ĐỘ',
      'paused': 'TẠM DỪNG',
      'error': 'LỖI',
      'completed': 'ĐÃ HOÀN THÀNH'
    };
    return statusMap[status] || status.toUpperCase();
  }

  getAvailableActions(profile) {
    const actions = [];
    const status = profile.statusEn || profile.status; // Use English status for logic

    // Check status first, then is_logged_in
    if (status === 'logging_in') {
      actions.push('complete');
    } else if (status === 'ready') {
      actions.push('start');
    } else if (status === 'running') {
      actions.push('pause', 'stop');
    } else if (status === 'paused') {
      actions.push('start', 'stop');
    } else if (!profile.is_logged_in) {
      actions.push('login');
    }

    return actions;
  }

  async updateProfile(profileId, updates) {
    try {
      // Update profile via API
      const apiUpdates = {
        name: updates.profileName || updates.name,
        description: updates.description,
        proxy_id: updates.proxy_id,
        status: updates.status,
        current_action: updates.current_action
      };

      // Remove undefined values
      Object.keys(apiUpdates).forEach(key => {
        if (apiUpdates[key] === undefined) {
          delete apiUpdates[key];
        }
      });

      const updatedProfile = await profileAPI.updateProfile(profileId, apiUpdates);
      const transformedProfile = this.transformProfileFromAPI(updatedProfile);

      // Update local profiles array
      const index = this.profiles.findIndex(p => p.id === profileId);
      if (index !== -1) {
        this.profiles[index] = transformedProfile;
        this.notifyListeners('profile_updated', transformedProfile);
      }

      return transformedProfile;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Failed to update profile:', apiError);
      throw new Error(apiError.message);
    }
  }

  async deleteProfile(profileId) {
    try {
      // Delete profile via API
      await profileAPI.deleteProfile(profileId);

      // Remove from local profiles array
      const index = this.profiles.findIndex(p => p.id === profileId);
      if (index !== -1) {
        const deletedProfile = this.profiles[index];
        this.profiles.splice(index, 1);
        this.notifyListeners('profile_deleted', deletedProfile);
      }

      return { success: true, message: 'Profile deleted successfully' };
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Failed to delete profile:', apiError);
      throw new Error(apiError.message);
    }
  }

  // Getters
  getProfiles() {
    return this.profiles;
  }

  getProfile(profileId) {
    return this.profiles.find(p => p.id === profileId);
  }

  getActiveProfiles() {
    return this.profiles.filter(p => p.isActive);
  }

  getLoggedInProfiles() {
    return this.profiles.filter(p => p.isLoggedIn);
  }
}

// Create singleton instance
const profileService = new ProfileService();

export default profileService;
